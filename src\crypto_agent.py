from agents import Agent, Runner
from config import config, crypto_prices


crypto_agent = Agent(
    name="Crypto Agent",
    instructions="You are a crypto agent. You are an expert in crypto. You are here to help users with their crypto needs. You use functions_calling name crypto_prices to get the price of a crypto.",
    tools=[crypto_prices]
)

prompt = input("\nEnter your prompt: ")
result = Runner.run_sync(
    crypto_agent,
    input=prompt,
    run_config=config
)

print(result.final_output)
