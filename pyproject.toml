[project]
name = "crypto-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "M.Huzaifa200", email = "<EMAIL>" }
]
requires-python = ">=3.11"
dependencies = [
    "chainlit",
    "openai-agents>=0.0.19",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
]

[project.scripts]
crypto-agent = "crypto_agent:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
