# 🪙 Crypto Agent 🤖

**Crypto Agent** is a smart AI assistant built with [Chainlit](https://docs.chainlit.io/)
that delivers **real-time cryptocurrency price updates**. Whether you’re curious about Bitcoin (BTC), Ethereum (ETH), or any other major coin, Crypto Agent is here to help — instantly and conversationally.

---

## 🚀 Features

- 💬 **Ask for live crypto prices** (BTC, ETH, BNB, etc.)
- 🤖 **Understands natural language** queries like:
  - "What's the price of Bitcoin?"
  - "Give me ETH and BNB prices"
- 🧠 **Session-based memory** – remembers conversation context while you're chatting
- 👋 **Automatic greeting** on start
- 👨‍💻 **Gives credit to the developer** when asked who created it

---

## 🛠️ Built With

- 🐍 [Python 3.11+](https://www.python.org/)
- 💬 [Chainlit](https://chainlit.io/) – for building conversational UIs
- 🔌 [OpenAI Agents](https://pypi.org/project/openai-agents/) – for structured agent logic
- 🌐 [Requests](https://docs.python-requests.org/) – for handling API calls
- 🔐 [python-dotenv](https://pypi.org/project/python-dotenv/) – for managing environment variables

# Take A look >> https://crypto-agent-0ay9.onrender.com/
