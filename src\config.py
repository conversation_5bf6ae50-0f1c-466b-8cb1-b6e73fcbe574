from agents import agent, Runner, AsyncOpenAI, OpenAIChatCompletionsModel, function_tool
from agents.run import RunConfig
from dotenv import load_dotenv
import os

load_dotenv()

gemini_api_key = os.getenv("GEMINI_API_KEY")

if not gemini_api_key:
    raise ValueError("GEMINI_API_KEY is not set")

client = AsyncOpenAI(
    api_key=gemini_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)

model = OpenAIChatCompletionsModel(
    model="gemini-2.0-flash",
    openai_client=client

)

config = RunConfig(
    model=model,
    model_provider=client,
    tracing_disabled=True
)


@function_tool
def crypto_prices(crypto: str):
    """
    Fetches the current price of a given cryptocurrency using CoinGecko's API.

    Parameters:
        crypto (str): The ID of the cryptocurrency (e.g., 'bitcoin', 'ethereum').

    Returns:
        dict: A dictionary containing the current price in USD.

    Raises:
        ValueError: If the CRYPTO_API_KEY environment variable is not set.
        Exception: If the API request fails or the response is invalid.
    """
    return

    load_dotenv()
    crypto_api_key = os.getenv("CRYPTO_API_KEY")

    if not crypto_api_key:
        raise ValueError("CRYPTO_API_KEY is not set")
